# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_barcode
# 
# Translators:
# <PERSON>, 2018
# <PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <jaro.b<PERSON><PERSON>@ekoenergo.sk>, 2018
# <AUTHOR> <EMAIL>, 2018
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~11.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-02 10:32+0000\n"
"PO-Revision-Date: 2018-10-02 10:32+0000\n"
"Last-Translator: gebri <<EMAIL>>, 2018\n"
"Language-Team: Slovak (https://www.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:44
#, python-format
msgid "Add product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:20
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_form_view_inherit
#, python-format
msgid "Barcode"
msgstr "Čiarový kód"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Adjustment Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Barcode Scanned"
msgstr "Čiarový kód naskenovaný"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:9
#, python-format
msgid "Barcode Scanning"
msgstr "Skenovanie čiarového kódu"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:226
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:202
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:25
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
#, python-format
msgid "Cancel"
msgstr "Zrušiť"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:12
#, python-format
msgid "Close"
msgstr "Zatvor"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Nakonfigurujte čiarové kódy produktov"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:158
#, python-format
msgid "Confirm"
msgstr "Potvrď"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:157
#, python-format
msgid "Discard"
msgstr "Zrušiť"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Zobrazovaný Názov"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:61
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:64
#, python-format
msgid "Don't show this message again"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:16
#, python-format
msgid "Download"
msgstr "Stiahnuť"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
msgid "Dummy"
msgstr "Maketa"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:100
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:123
#, python-format
msgid "Edit"
msgstr "Upraviť"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "From"
msgstr "Od"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:59
#, python-format
msgid "From:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_inventory_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "Medzinárodné číslo položky použité pre identifikáciu výrobku"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory
msgid "Inventory"
msgstr "Sklad"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:37
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
#, python-format
msgid "Inventory Adjustments"
msgstr "Inventárne úpravy"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "Inventory Details"
msgstr "Detaily inventára"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory_line
msgid "Inventory Line"
msgstr "Inventárny riadok"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Umiestnenia inventára"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Posledná modifikácia"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:70
#, python-format
msgid "Leave it"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Šarža"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:109
#, python-format
msgid "Lot/Serial Number Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Riadok pohybu"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_inventory_action_new_inventory
msgid "New Inventory"
msgstr "Nové zásoby"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:49
#, python-format
msgid "Next"
msgstr "Ďalší"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:93
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:28
#, python-format
msgid "No picking corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:26
#, python-format
msgid "No picking or location corresponding to barcode %(barcode)s"
msgstr "Žiadny výber alebo lokácia nezodpovedajú čiarovému kódu %(barcode)s"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open a picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:106
#: code:addons/stock_barcode/models/stock_picking.py:313
#: code:addons/stock_barcode/models/stock_picking.py:331
#, python-format
msgid "Open picking form"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:34
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Operácie"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Vyberanie"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:86
#, python-format
msgid "Picking %s"
msgstr "Výber %s"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Typ výberu"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:48
#, python-format
msgid "Previous"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:21
#, python-format
msgid "Print Delivery Slip"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:23
#, python-format
msgid "Print Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:20
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
msgid "Product"
msgstr "Produkt"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Čiarové kódy produktu"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:45
#, python-format
msgid "Put In Pack"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Množstvo"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Zarezervované množstvo"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_line_barcode
msgid "Real Quantity"
msgstr "Reálne množstvo"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:67
#, python-format
msgid "Remove it"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "Scan a"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "Scan an"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:33
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:32
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:31
#, python-format
msgid "Scan products"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:34
#, python-format
msgid "Scan the serial or lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:35
#, python-format
msgid "Scan the source location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory__scan_location_id
msgid "Scanned Location"
msgstr "Naskenovaná lokácia"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1327
#, python-format
msgid "Scanning is disabled in this state."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:22
#: model:ir.model,name:stock_barcode.model_stock_scrap
#, python-format
msgid "Scrap"
msgstr "Vyradené"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:205
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:165
#, python-format
msgid "Success"
msgstr "Úspech"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:303
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""
"Čiarový kód \"%(barcode)s\" nezodpovedá s riadnym produktom, balíkom alebo "
"lokáciou."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:226
#, python-format
msgid "The inventory adjustment has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:205
#, python-format
msgid "The inventory adjustment has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:87
#, python-format
msgid "The picking is %s and cannot be edited."
msgstr "Výber je %s a nemôže byť upravovaný."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1113
#, python-format
msgid "The scanned lot does not match an existing one."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1160
#, python-format
msgid "The scanned serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:202
#, python-format
msgid "The transfer has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:165
#, python-format
msgid "The transfer has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:38
#, python-format
msgid "This inventory adjustment is already done"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:782
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1198
#, python-format
msgid "This location is not a child of the main location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:964
#, python-format
msgid "This package is already scanned."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:37
#, python-format
msgid "This picking is already cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:36
#, python-format
msgid "This picking is already done"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "To"
msgstr "Pre"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:60
#, python-format
msgid "To:"
msgstr "Komu:"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
msgid "Transfer"
msgstr "Prevod"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Merná jednotka"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:24
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:50
#, python-format
msgid "Validate"
msgstr "Overiť"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Hodnota posledného naskenovaného čiarového kódu."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:594
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1327
#, python-format
msgid "Warning"
msgstr "Varovanie"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:12
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:302
#, python-format
msgid "Wrong barcode"
msgstr "Nesprávny čiarový kód"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:819
#, python-format
msgid "You are expected to scan a source location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:892
#, python-format
msgid "You are expected to scan more products or a destination location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:881
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1203
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking's location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:883
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1205
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:58
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:133
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:16
#, python-format
msgid "commands for Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "document"
msgstr "dokument"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#, python-format
msgid "location"
msgstr "lokácia"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "operation type"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "stock barcodes sheet"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#, python-format
msgid "to create a new transfer from this location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "to create a new transfer."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "to open it."
msgstr ""
