# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON><PERSON><PERSON> <durdi<PERSON>.<PERSON><PERSON>@storm.hr>, 2019
# <PERSON><PERSON><PERSON> <ivic<PERSON>.<PERSON><PERSON><PERSON>@storm.hr>, 2019
# <PERSON><PERSON><PERSON> <karol<PERSON>.ton<PERSON>@storm.hr>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:05+0000\n"
"PO-Revision-Date: 2019-08-26 09:38+0000\n"
"Last-Translator: <PERSON>, 2019\n"
"Language-Team: Croatian (https://www.transifex.com/odoo/teams/41243/hr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hr\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Add product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_form_view_inherit
#, python-format
msgid "Barcode"
msgstr "Barkod"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Adjustment Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Barkod nomenklatura"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Barcode Scanned"
msgstr "Barkod čitač"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "Skeniranje barkodova"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
#, python-format
msgid "Cancel"
msgstr "Odustani"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Close"
msgstr "Zatvori"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Postavke"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Konfiguriraj barkodove artikala"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Confirm"
msgstr "Potvrdi"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Kreirao"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Kreirano"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid ""
"Delivery Packages needs to be enabled in Inventory Settings to use packages"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Discard"
msgstr "Odbaci"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Naziv"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"Želite li trajno izbrisati ovu poruku?\n"
"Poruka se više neće pojavljivati pa budite sigurni da Vam više neće trebati stranice s barkodovima ili da imate kopiju."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Don't show this message again"
msgstr "Ne prikazuj ponovno ovu poruku"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Download"
msgstr "Preuzimanje"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
msgid "Dummy"
msgstr "Prazan"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Edit"
msgstr "Uredi"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "From"
msgstr "Od"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "From:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "Interna šifra proizvoda koja se koristi za identifikaciju artikla."

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory
msgid "Inventory"
msgstr "Skladište"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
#, python-format
msgid "Inventory Adjustments"
msgstr "Unos inventure"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "Inventory Details"
msgstr "Detalji inventure"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory_line
msgid "Inventory Line"
msgstr "Stavka inventure"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Lokacije inventure"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Zadnja promjena"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Promijenio"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Vrijeme promjene"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Leave it"
msgstr "Ostavi"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Lot"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "Detalji lot/serijskog broja"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Stavka temeljnice"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_inventory_action_new_inventory
msgid "New Inventory"
msgstr "Novo skladište"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Next"
msgstr "Sljedeći"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid "No picking corresponding to barcode %(barcode)s"
msgstr "Nijedna skladišnica ne odgovara barkodu %(barcode)s"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid "No picking or location corresponding to barcode %(barcode)s"
msgstr "Nijedna skladišnica ili lokacija ne odgovara barkodu %(barcode)s"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenklatrua"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open a picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#: code:addons/stock_barcode/models/stock_picking.py:0
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Open picking form"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Operacije"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Skladišnice"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:0
#, python-format
msgid "Picking %s"
msgstr "Skladišnica %s"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Vrsta dokumenta"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Previous"
msgstr "Prethodni"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Barcodes PDF"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Barcodes ZPL"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Delivery Slip"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
msgid "Product"
msgstr "Proizvod"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Barkodovi proizvoda"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Put In Pack"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Količina"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Rezervirana količina"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_line_barcode
msgid "Real Quantity"
msgstr "Realna količina"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Remove it"
msgstr "Ukloni"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Scan a"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Scan an"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan products"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan the serial or lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan the source location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "Scanning is disabled in this state."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#, python-format
msgid "Scrap"
msgstr "Otpis"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "Success"
msgstr "Uspjeh"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr "Barkod \"%(barcode)s\" ne odgovara proizvodu, pakiranju ili lokaciji."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#, python-format
msgid "The inventory adjustment has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited."
msgstr "Skladišnica je %s i nije ju moguće uređivati."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "The scanned lot does not match an existing one."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "The transfer has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This inventory adjustment is already done"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "This location is not a child of the main location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "This package is already scanned."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This picking is already cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This picking is already done"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "To"
msgstr "Do"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "To:"
msgstr "Za:"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
msgid "Transfer"
msgstr "Prijenos"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Jedinica mjere"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Validate"
msgstr "Ovjeri"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Vrijednost posljednjeg skeniranog barkoda."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "Warning"
msgstr "Upozorenje"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "Pogrešan barkod"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan a source location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan more products or a destination location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking's location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_inventory.py:0
#, python-format
msgid "You must define a warehouse for the company: %s."
msgstr "Morate definirati skladište za tvrtku: %s."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "commands for Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "document"
msgstr "dokument"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "location"
msgstr "lokacija"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "operation type"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr "zapis skladišnih barkodova"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to create a new transfer."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to open it."
msgstr ""
