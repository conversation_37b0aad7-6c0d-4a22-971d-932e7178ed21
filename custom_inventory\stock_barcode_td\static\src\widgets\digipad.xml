<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="stock_barcode_td.DigipadTemplate">
        <div class="o_digipad_widget">
            <div class="o_digipad_digit_buttons mr-2">
                <t t-foreach="widget.buttons" t-as="button"
                    t-call="stock_barcode_td.DigipadButtonsTemplate"/>
            </div>
            <div class="o_digipad_special_buttons">
                <t t-foreach="widget.specialButtons" t-as="button"
                    t-call="stock_barcode_td.DigipadSpecialButtonsTemplate"/>
            </div>
        </div>
    </t>

    <t t-name="stock_barcode_td.DigipadButtonsTemplate">
        <div t-att-data-button="button" class="o_digipad_button btn btn-primary d-flex justify-content-center align-items-center border w-100 py-2">
            <div t-if="button == 'erase'" class="fa fa-lg fa-long-arrow-left"/>
            <div t-else="" t-esc="button"/>
        </div>
    </t>

    <t t-name="stock_barcode_td.DigipadSpecialButtonsTemplate">
        <!-- +1 / -1 buttons -->
        <div t-if="button.length" class="o_digipad_button btn btn-secondary d-flex justify-content-center align-items-center border w-100 py-2"
            t-att-data-button="button" t-esc="button == 'increase' ? '+1' : '-1'"/>
        <!-- Product packagings buttons -->
        <div t-else="" class="o_packaging_button btn btn-secondary border w-100 py-2"
            t-att-data-qty="button.qty">
            <div class="text-capitalize">
                +<t t-esc="button.qty"/>
                <span t-if="widget.display_uom" class="small-text" t-esc="button.product_uom_id[1]"/>
            </div>
            <div name="packaging_name" class="small-text" t-esc="button.name"/>
        </div>
    </t>

</templates>
