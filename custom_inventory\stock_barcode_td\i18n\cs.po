# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2021
# <PERSON><PERSON><PERSON>emec <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# <PERSON> <jan.<PERSON><PERSON><PERSON>@centrum.cz>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2021
# <AUTHOR> <EMAIL>, 2021
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Czech (https://www.transifex.com/odoo/teams/41243/cs/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: cs\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n >= 2 && n <= 4 && n % 1 == 0) ? 1: (n % 1 != 0 ) ? 2 : 3;\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Source Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\" "
"attrs=\"{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Counted Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cubes mr-3\" title=\"Locations\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker mr-3\" title=\"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center mr-3\" title=\"Source "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-tags mr-3\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-user-o mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-user-o text-center mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "<span> / </span>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<span>On Hand</span>"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Add Product"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Přidělení"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#, python-format
msgid "Barcode"
msgstr "Čárový kód"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenklatura čárového kódu"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "Naskenovaný čárový kód"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "Skenování čárových kódů"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Cancel"
msgstr "Zrušit"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Close"
msgstr "Zavřít"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavení konfigurace"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Konfigurace čárových kódů produktu"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Confirm"
msgstr "Potvrdit"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "Započítané množství"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "Vytvořit nový přenos"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Vytvořeno od"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Vytvořeno"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr "Výchozí pohyb"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Description"
msgstr "Popis"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_dest_id
msgid "Destination Location"
msgstr "Cílové umístění"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "Cílový balíček"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Discard"
msgstr "Zrušit"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Zobrazované jméno"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"Chcete tuto zprávu trvale odebrat?\n"
"Už se neobjeví, takže se ujistěte, že nepotřebujete list s čárovými kódy nebo kopii."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Don't show this message again"
msgstr "Tuto zprávu znovu nezobrazovat"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Download"
msgstr "Stáhnout"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "Prázdný"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "From"
msgstr "Od"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr "V #{kanban_getcolorname(record.color.raw_value)}"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "Mezinárodní číslo položky použité pro identifikaci výrobku."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Úprava inventáře"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Inventory Adjustments"
msgstr "Úpravy inventáře"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Umístění (množ.) inventáře"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr "Produktové řady LN / SN"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Naposled změněno"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Naposledy upraveno od"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Naposled upraveno"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Leave it"
msgstr "Nechat jak je"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "Místo"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "Místo bylo zpracováno"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Dávka"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Šarže / seriové číslo"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "Detaily šarže / sériového čísla"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Řadek pohybu"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Next"
msgstr "Další"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No %s ready for this product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "Žádný typ interní operace. Zkontrolujte, prosím, v nastavení skladu."

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No product found for barcode %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Systém pojmenování"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.picking_view_kanban_inherit_barcode
msgid "Open picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
#, python-format
msgid "Open picking form"
msgstr "Otevčít formulář výdeje"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Úkony"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "Majitel"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "Balení"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Package type %s was correctly applied to the package %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "Balíky"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Dodání"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "Detaily výdeje"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Druh dodeje"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.js:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Please, Scan again !"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Previous"
msgstr "Předchozí"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Barcodes PDF"
msgstr "Tisk PDF čárových kódů "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Delivery Slip"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Print Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "Produkt"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Čárové kódy produktu"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Pohyby produktu (trasa pohybu zboží)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "Balení produktu"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr "Skladové množství produktu"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Měrná jednotka výrobku"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Put In Pack"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr "Rezervované množství"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Množství"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Rezervované množství"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "Množství"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Remove it"
msgstr "Odebrat to"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan a"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan a product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Scan a product to filter the transfers."
msgstr "Naskenujte produkt a filtrujte převody."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/scan_product_tip.xml:0
#, python-format
msgid "Scan a product to filter your records"
msgstr "Skenování produktu pro filtrování záznamů"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan an"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr "Skenovat více produktů nebo skenovat nové umístění zdroje"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr "Naskenujte více produktů nebo naskenujte cílové umístění"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the serial number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the source location, or scan a product"
msgstr "Naskenujte zdrojové umístění nebo naskenujte produkt"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid ""
"Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible "
"with the product's one (%s)."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_scrap
msgid "Scrap"
msgstr "Zmetek"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "Vybrat produkt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "Seriové číslo / šarže"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "Zdrojové umístění"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Tap to scan"
msgstr "Klepnutím skenovat"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""
"Čárový kód \"%(barcode)s\" neodpovídá správnému produktu, balíčku nebo "
"umístění."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr "Úprava zásob byla ověřena"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr "Naskenované sériové číslo je již používáno."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr "Převod byl zrušen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "This package is already scanned."
msgstr "Tento balík je již naskenován."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is already done"
msgstr "Toto vyzvednutí je již hotové"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "This serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "To"
msgstr "Do"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr "Zpracovat"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "To use packages, enable 'Delivery Packages' from the settings"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "Převod"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "Převody umožňují přesouvat produkty z jednoho místa na druhé."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Měrná jednotka"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "UoM"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Validate"
msgstr "Ověřit"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr "Ověřit produktové číslo"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Hodnota posledního naskenovaného čárového kódu."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""
"Vytvořili jsme několik demonstračních dat s čárovými kódy, abyste mohli "
"prozkoumat funkce. Tiskněte"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Wrong Unit of Measure"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "Špatný čárový kód"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr "Nelze skenovat dvakrát stejné sériové číslo"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "commands for Inventory"
msgstr "příkazy pro Inventář"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "document"
msgstr "dokument"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "location"
msgstr "lokace"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "operation type"
msgstr "typ operace"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "product"
msgstr "produkt"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr "k vytvoření nového přenosu z tohoto místa."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer."
msgstr "k vytvoření nového převodu."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to open it."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to show its location and quantity."
msgstr "zobrazit jeho umístění a množství."
