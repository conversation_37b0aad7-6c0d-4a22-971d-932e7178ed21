<?xml version="1.0" encoding="utf-8"?>
<odoo><data>
    <record id="stock_picking_view_form_inherit_stock_barcode" model="ir.ui.view">
        <field name="name">stock.picking.form.td.inherit</field>
        <field name="model">stock.picking.td</field>
        <field name="inherit_id" ref="stock_td.view_picking_form"/>
        <field name="arch" type="xml">
            <form position="inside">
                <field name="_barcode_scanned" widget="picking_barcode_handler_td"/>
            </form>
            <xpath expr="//button[@name='button_validate']" position="attributes">
                <attribute name="barcode_trigger">validate</attribute>
            </xpath>
            <xpath expr="//button[@name='action_cancel']" position="attributes">
                <attribute name="barcode_trigger">cancel</attribute>
            </xpath>
            <xpath expr="//button[@name='do_print_picking']" position="attributes">
                <attribute name="barcode_trigger">print-op</attribute>
            </xpath>
            <xpath expr="//button[@name='%(stock_td.action_report_delivery)d']" position="attributes">
                <attribute name="barcode_trigger">print-slip</attribute>
            </xpath>
            <xpath expr="//button[@name='action_put_in_pack']" position="attributes">
                <attribute name="barcode_trigger">pack</attribute>
            </xpath>
            <xpath expr="//button[@name='button_scrap']" position="attributes">
                <attribute name="barcode_trigger">scrap</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_detailed_operation_tree_inherit_stock_barcode" model="ir.ui.view">
        <field name="name">stock.move.line.operations.tree.td.inherit</field>
        <field name="model">stock.move.line.td</field>
        <field name="inherit_id" ref="stock_td.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="inside">
                <field name="product_barcode" invisible="1" optional="hide"/>
                <field name="location_processed" invisible="1" optional="hide"/>
            </xpath>
            <xpath expr="//field[@name='qty_done']" position="attributes">
                <attribute name="options">{'barcode_events': True}</attribute>
                <attribute name="widget">field_float_scannable</attribute>
            </xpath>
        </field>
    </record>

    <record id="view_stock_move_line_kanban_inherited" model="ir.ui.view">
        <field name="name">stock.move.line.kanban.td.inherited</field>
        <field name="model">stock.move.line.td</field>
        <field name="inherit_id" ref="stock_td.view_stock_move_line_kanban"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='qty_done']" position="after">
                <field name="product_barcode" invisible="1"/>
                <field name="location_processed" invisible="1"/>
                <field name="result_package_id" invisible="1"/>
                <field name="lots_visible" invisible="1"/>
            </xpath>
        </field>
    </record>

    <record id="stock_picking_barcode" model="ir.ui.view">
        <field name="name">stock.picking.form.view.barcode.td</field>
        <field name="model">stock.picking.td</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form string="Picking Details">
                <field name="picking_type_code" invisible="1"/>
                <group>
                    <button name="action_view_reception_report" string="Allocation"
                        context="{'default_picking_ids': [id]}"
                        class="btn btn-primary o_reception_report" icon="fa-list"
                        attrs="{'invisible': [('picking_type_code', '!=', 'incoming')]}"
                        groups="stock.group_reception_report"/>
                    <field name="partner_id"/>
                    <field name="scheduled_date" readonly="1" attrs="{'invisible': [('scheduled_date', '=', False)]}"/>
                    <field name="origin" readonly="1" attrs="{'invisible': [('origin', '=', False)]}"/>
                    <field name="state" readonly="1" attrs="{'invisible': [('state', '=', False)]}"/>
                    <field name="priority" readonly="1" attrs="{'invisible': [('priority', '=', False)]}"/>
                    <field name="owner_id" readonly="1" attrs="{'invisible': [('owner_id', '=', False)]}" groups="stock.group_tracking_owner"/>
                    <field name="company_id" readonly="1" groups="base.group_multi_company"/>
                    <field name="move_lines" readonly="1" attrs="{'invisible': [('move_lines', '=', False)]}">
                        <tree>
                            <field name="product_id"/>
                            <field name="description_picking" string="Description"/>
                            <field name="product_uom_qty"/>
                            <field name="state"/>
                        </tree>
                    </field>
                    <field name="note" readonly="1" attrs="{'invisible': [('note', '=', False)]}"/>
                </group>
            </form>
        </field>
    </record>

    <record id="open_picking" model="ir.actions.act_window">
        <field name="name">Open picking form</field>
        <field name="res_model">stock.picking.td</field>
        <field name="view_mode">form</field>
        <field name="context">{
            'res_id': active_id,
        }
        </field>
    </record>

    <record id="picking_view_kanban_inherit_barcode" model="ir.ui.view">
        <field name="name">stock.picking.view.kanban.barcode.td</field>
        <field name="model">stock.picking.td</field>
        <field name="inherit_id" ref="stock_td.stock_picking_kanban"/>
        <field name="arch" type="xml">

            <!-- Use the form view -->
            <xpath expr="//div[hasclass('o_kanban_record_headings')]" position="after">
                <button style="padding: 0px 10px;" class="btn btn-link fa fa-desktop" title="Open picking" name="action_open_picking" type="object"/>
            </xpath>

            <!-- Use mobile view-->
            <xpath expr="//strong[hasclass('o_kanban_record_title')]" position="replace">
                <button style="padding: 0px 0px;" class="btn btn-link o_kanban_record_title" name="action_open_picking_client_action"
                  type="object">
                  <span><t t-esc="record.name.value"/></span>
                </button>
            </xpath>
        </field>
    </record>

    <record id="view_picking_type_form_inherit_stock_barcode" model="ir.ui.view">
        <field name="name">Operation Types</field>
        <field name="model">stock.picking.type.td</field>
        <field name="inherit_id" ref="stock_td.view_picking_type_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='warehouse_id']" position="after">
                <field name="barcode"/>
            </xpath>
        </field>
    </record>
</data>
</odoo>
