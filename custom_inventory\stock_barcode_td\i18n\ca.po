# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_barcode
# 
# Translators:
# <PERSON> i Bochaca <<EMAIL>>, 2019
# <PERSON><PERSON> <man<PERSON><PERSON>@outlook.com>, 2019
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.2+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-03-20 14:08+0000\n"
"PO-Revision-Date: 2016-08-05 13:32+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2019\n"
"Language-Team: Catalan (https://www.transifex.com/odoo/teams/41243/ca/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ca\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:48
#, python-format
msgid "Add product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:20
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_form_view_inherit
#, python-format
msgid "Barcode"
msgstr "Codi de barres"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Adjustment Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura del codi de barres"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Barcode Scanned"
msgstr "Codi de barres escanejat"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:9
#, python-format
msgid "Barcode Scanning"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:226
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:208
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:25
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
#, python-format
msgid "Cancel"
msgstr "Cancel·la"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:12
#, python-format
msgid "Close"
msgstr "Tancar"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:162
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Creat per"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Creat el"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:328
#, python-format
msgid ""
"Delivery Packages needs to be enabled in Inventory Settings to use packages"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:161
#, python-format
msgid "Discard"
msgstr "Rebutgi"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Mostrar Nom"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:60
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:63
#, python-format
msgid "Don't show this message again"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:16
#, python-format
msgid "Download"
msgstr "Descarregar"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
msgid "Dummy"
msgstr "Fictici"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:104
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:127
#, python-format
msgid "Edit"
msgstr "Editar"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "From"
msgstr "De"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:63
#, python-format
msgid "From:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_inventory_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr ""
"Número d'article internacional utilitzat per la identificació de producte."

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory
msgid "Inventory"
msgstr "Inventari"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:37
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
#, python-format
msgid "Inventory Adjustments"
msgstr "Ajustaments d'inventari"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "Inventory Details"
msgstr "Detalls de l'inventari"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory_line
msgid "Inventory Line"
msgstr "Línia inventari"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicacions d'inventari"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Última modificació el "

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Última actualització per"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Última actualització el"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:69
#, python-format
msgid "Leave it"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Lot"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:117
#, python-format
msgid "Lot/Serial Number Details"
msgstr "Detalls dels números de Lot/Sèrie"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Línia de moviment"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_inventory_action_new_inventory
msgid "New Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:53
#, python-format
msgid "Next"
msgstr "Següent"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:93
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:28
#, python-format
msgid "No picking corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:26
#, python-format
msgid "No picking or location corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open a picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:106
#: code:addons/stock_barcode/models/stock_picking.py:321
#: code:addons/stock_barcode/models/stock_picking.py:339
#, python-format
msgid "Open picking form"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:34
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Operacions"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Albarà"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:86
#, python-format
msgid "Picking %s"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipus de picking "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:52
#, python-format
msgid "Previous"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:21
#, python-format
msgid "Print Delivery Slip"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:23
#, python-format
msgid "Print Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:20
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
msgid "Product"
msgstr "Producte"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Codi de barres de productes"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:49
#, python-format
msgid "Put In Pack"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Quantitat"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Quantitat reservada"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_line_barcode
msgid "Real Quantity"
msgstr "Quantitat real"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:66
#, python-format
msgid "Remove it"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "Scan a"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "Scan an"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:37
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:36
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:35
#, python-format
msgid "Scan products"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:38
#, python-format
msgid "Scan the serial or lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:39
#, python-format
msgid "Scan the source location"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory__scan_location_id
msgid "Scanned Location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1346
#, python-format
msgid "Scanning is disabled in this state."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:22
#: model:ir.model,name:stock_barcode.model_stock_scrap
#, python-format
msgid "Scrap"
msgstr "Deixalla"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:205
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:171
#, python-format
msgid "Success"
msgstr "Èxit"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:311
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:226
#, python-format
msgid "The inventory adjustment has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:205
#, python-format
msgid "The inventory adjustment has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:87
#, python-format
msgid "The picking is %s and cannot be edited."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1132
#, python-format
msgid "The scanned lot does not match an existing one."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1179
#, python-format
msgid "The scanned serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:208
#, python-format
msgid "The transfer has been cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:171
#, python-format
msgid "The transfer has been validated"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:42
#, python-format
msgid "This inventory adjustment is already done"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:786
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1217
#, python-format
msgid "This location is not a child of the main location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:968
#, python-format
msgid "This package is already scanned."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:41
#, python-format
msgid "This picking is already cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:40
#, python-format
msgid "This picking is already done"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "To"
msgstr "Per"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:64
#, python-format
msgid "To:"
msgstr "Per:"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
msgid "Transfer"
msgstr "Transferència"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Unitat de mesura"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:24
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:54
#, python-format
msgid "Validate"
msgstr "Validar"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valor de l'últim codi de barres escanejat."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:593
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1346
#, python-format
msgid "Warning"
msgstr "Avís"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:12
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:310
#, python-format
msgid "Wrong barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:823
#, python-format
msgid "You are expected to scan a source location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:896
#, python-format
msgid "You are expected to scan more products or a destination location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:885
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1222
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking's location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:887
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:1224
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:58
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:133
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:16
#, python-format
msgid "commands for Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "document"
msgstr "document"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#, python-format
msgid "location"
msgstr "ubicació"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "operation type"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "stock barcodes sheet"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:26
#, python-format
msgid "to create a new transfer from this location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:25
#, python-format
msgid "to create a new transfer."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:27
#, python-format
msgid "to open it."
msgstr ""
