odoo.define('stock_barcode_td.stock_picking_barcode_tests', function (require) {
"use strict";

var testUtils = require('web.test_utils');
var FormView = require('web.FormView');

var createView = testUtils.createView;
var triggerKeypressEvent = testUtils.dom.triggerKeypressEvent;

const { createWebClient, doAction } = require('@web/../tests/webclient/helpers');

QUnit.module('stock_barcode_td', {}, function () {

QUnit.module('Barcode', {
    beforeEach: function () {
        var self = this;

        this.clientData = {
            action: {
                tag: 'stock_barcode_client_action',
                type: 'ir.actions.client',
                res_model: "stock.picking.td",
                context: {},
            },
            currentState: {
                actions: {},
                data: {
                    records: {
                        'barcode.nomenclature': [{
                            id: 1,
                            rule_ids: [],
                        }],
                        'stock.location.td': [],
                        'stock.move.line.td': [],
                        'stock.picking.td': [],
                    },
                    nomenclature_id: 1,
                    source_location_ids: [],
                    destination_locations_ids: [],
                },
                groups: {},
            },
        };
        this.mockRPC = function (route, args) {
            if (route === '/stock_barcode_td/get_barcode_data') {
                return Promise.resolve(self.clientData.currentState);
            } else if (route === '/stock_barcode_td/static/img/barcode.svg') {
                return Promise.resolve();
            }
        };
        this.data = {
            product: {
                fields: {
                    name: {string : "Product name", type: "char"},
                },
                records: [{
                    id: 1,
                    name: "Large Cabinet",
                }, {
                    id: 4,
                    name: "Cabinet with Doors",
                }],
            },
            'stock.move.line.td': {
                fields: {
                    product_id: {string: "Product", type: 'many2one', relation: 'product'},
                    product_qty: {string: "To Do", type: 'float', digits: [16,1]},
                    qty_done: {string: "Done", type: 'float', digits: [16,1]},
                    product_barcode: {string: "Product Barcode", type: 'char'},
                    lots_visible: {string: "Product tracked by lots", type: 'boolean'},
                },
                records: [{
                    id: 3,
                    product_id: 1,
                    product_qty: 2.0,
                    qty_done: 0.0,
                    product_barcode: "543982671252",
                }, {
                    id: 5,
                    product_id: 4,
                    product_qty: 2.0,
                    qty_done: 0.0,
                    product_barcode: "678582670967",
                }],
            },
            stock_picking: {
                fields: {
                    _barcode_scanned: {string: "Barcode Scanned", type: 'char'},
                    move_line_ids_without_package: {
                        string: "one2many field",
                        relation: 'stock.move.line.td',
                        type: 'one2many',
                    },
                },
                records: [{
                    id: 2,
                    move_line_ids_without_package: [3],
                }, {
                    id: 5,
                    move_line_ids_without_package: [5],
                }],
            },
        };
    }
});

QUnit.test('scan a product (no tracking)', async function (assert) {
    assert.expect(5);

    var form = await createView({
        View: FormView,
        model: 'stock_picking_td',
        data: this.data,
        arch: '<form string="Products">' +
                '<field name="_barcode_scanned" widget="picking_barcode_handler_td"/>' +
                '<sheet>' +
                    '<notebook>' +
                        '<page string="Operations">' +
                            '<field name="move_line_ids_without_package">' +
                                '<tree>' +
                                    '<field name="product_id"/>' +
                                    '<field name="product_qty"/>' +
                                    '<field name="qty_done"/>' +
                                    '<field name="product_barcode"/>' +
                                    '<field name="lots_visible" invisible="1"/>' +
                                '</tree>' +
                            '</field>' +
                        '</page>' +
                    '</notebook>' +
                '</sheet>' +
            '</form>',
        res_id: 2,
        mockRPC: function (route, args) {
            assert.step(args.method);
            return this._super.apply(this, arguments);
        },
        viewOptions: {
            mode: 'edit',
        },
    });

    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '0.0',
        "quantity done should be 0");

    _.each(['5','4','3','9','8','2','6','7','1','2','5','2','Enter'], triggerKeypressEvent);
    await testUtils.nextTick();
    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '1.0',
        "quantity done should be 1");
    assert.verifySteps(['read', 'read'], "no RPC should have been done for the barcode scanned");

    form.destroy();
});

QUnit.test('scan a product tracked by lot', async function (assert) {
    assert.expect(8);

    // simulate a PO for a tracked by lot product
    this.data['stock.move.line.td'].records[0].lots_visible = true;

    var form = await createView({
        View: FormView,
        model: 'stock_picking_td',
        data: this.data,
        arch: '<form string="Products">' +
                '<field name="_barcode_scanned" widget="picking_barcode_handler_td"/>' +
                '<sheet>' +
                    '<notebook>' +
                        '<page string="Operations">' +
                            '<field name="display_name"/>' +
                            '<field name="move_line_ids_without_package">' +
                                '<tree>' +
                                    '<field name="product_id"/>' +
                                    '<field name="product_qty"/>' +
                                    '<field name="qty_done"/>' +
                                    '<field name="product_barcode"/>' +
                                    '<field name="lots_visible" invisible="1"/>' +
                                '</tree>' +
                            '</field>' +
                        '</page>' +
                    '</notebook>' +
                '</sheet>' +
            '</form>',
        res_id: 2,
        mockRPC: function (route, args) {
            assert.step(args.method);
            if (args.method === 'get_po_to_split_from_barcode') {
                return Promise.resolve({action_id: 1});
            }
            return this._super.apply(this, arguments);
        },
        intercepts: {
            do_action: function (event) {
                assert.deepEqual(event.data.action, {action_id: 1}, "should trigger a do_action");
            },
        },
        viewOptions: {
            mode: 'edit',
        },
    });

    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '0.0',
        "quantity done should be 0");

    // trigger a change on a field to be able to check that the record is correctly
    // saved before calling get_po_to_split_from_barcode
    await testUtils.fields.editInput(form.$('.o_field_widget[name="display_name"]'), 'new value');
    _.each(['5','4','3','9','8','2','6','7','1','2','5','2','Enter'], triggerKeypressEvent);
    await testUtils.nextTick();
    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '0.0',
        "quantity done should still be 0");
    assert.verifySteps(['read', 'read', 'write', 'get_po_to_split_from_barcode'],
        "get_po_to_split_from_barcode method call verified");

    form.destroy();
});

QUnit.test('scan a product verify onChange', async function (assert) {
    assert.expect(7);

    this.data.stock_picking.onchanges = {
        move_line_ids: function () {},
    };
    this.data['stock.move.line.td'].onchanges = {
        qty_done: function () {},
    };
    var form = await createView({
        View: FormView,
        model: 'stock_picking_td',
        data: this.data,
        arch: '<form string="Products">' +
                '<field name="_barcode_scanned" widget="picking_barcode_handler_td"/>' +
                '<sheet>' +
                    '<notebook>' +
                        '<page string="Operations">' +
                            '<field name="display_name"/>' +
                            '<field name="move_line_ids_without_package">' +
                                '<tree>' +
                                    '<field name="product_id"/>' +
                                    '<field name="product_qty"/>' +
                                    '<field name="qty_done"/>' +
                                    '<field name="product_barcode"/>' +
                                '</tree>' +
                            '</field>' +
                        '</page>' +
                    '</notebook>' +
                '</sheet>' +
            '</form>',
        res_id: 2,
        mockRPC: function (route, args) {
            assert.step(args.method);
            return this._super.apply(this, arguments);
        },
        viewOptions: {
            mode: 'edit',
        },
    });

    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '0.0',
        "quantity done should be 0");

    assert.strictEqual(form.activeBarcode._barcode_scanned.notifyChange, false,
        "_barcode_scanned should not notify change");

    // trigger a change on a field to be able to check that the record is correctly
    // saved before calling get_po_to_split_from_barcode
    await testUtils.fields.editInput(form.$('.o_field_widget[name="display_name"]'), 'new value');
    _.each(['5','4','3','9','8','2','6','7','1','2','5','2','Enter'], triggerKeypressEvent);
    await testUtils.nextTick();
    assert.strictEqual(form.$('.o_data_row .o_data_cell:nth(2)').text(), '1.0',
        "quantity done should be 1");
    // We won't be able to block onchange calls on x2many since the notifyChange
    // is not propagated in basic model.
    assert.verifySteps(['read', 'read', 'onchange']);

    form.destroy();
});

QUnit.test('exclamation-triangle when picking is done', async function (assert) {
    assert.expect(1);
    const pickingRecord = {
        id: 2,
        state: 'done',
        move_line_ids: [],
    };
    this.clientData.action.context.active_id = pickingRecord.id;
    this.clientData.currentState.data.records['stock.picking'].push(pickingRecord);
    const webClient = await createWebClient({
        mockRPC: this.mockRPC,
    });
    await doAction(webClient, this.clientData.action);
    assert.containsOnce(webClient, '.fa-5x.fa-exclamation-triangle:not(.d-none)', "Should have warning icon");
});

});
});
