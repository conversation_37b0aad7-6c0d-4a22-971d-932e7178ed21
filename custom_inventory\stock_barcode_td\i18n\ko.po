# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# <AUTHOR> <EMAIL>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: Linkup <<EMAIL>>, 2021\n"
"Language-Team: Korean (https://www.transifex.com/odoo/teams/41243/ko/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: ko\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Source Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\" "
"attrs=\"{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Counted Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cubes mr-3\" title=\"Locations\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker mr-3\" title=\"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center mr-3\" title=\"Source "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-tags mr-3\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-user-o mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-user-o text-center mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> 바코드 명령 인쇄"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "<span> / </span>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<span>On Hand</span>"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Add Product"
msgstr "상품 추가"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "할당"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#, python-format
msgid "Barcode"
msgstr "바코드"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "바코드 명칭"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "선별 클라이언트 작업 바코드"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "바코드 스캔됨"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "바코드 스캐닝"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Cancel"
msgstr "취소"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Close"
msgstr "닫기"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "설정 구성"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "상품 바코드 설정"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Confirm"
msgstr "승인"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "연락처"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "계산된 수량"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "새 이송 만들기"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "작성자"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "작성일자"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr "기본 이동"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Description"
msgstr "설명"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_dest_id
msgid "Destination Location"
msgstr "목적지 위치"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "대상 꾸러미 상품"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Discard"
msgstr "작성 취소"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "표시명"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"영구적으로 메시지를 삭제하시겠습니까 ?\n"
"                더이상 표시하지 않으므로, 바코드시트 또는 사본이 필요 없습니다."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Don't show this message again"
msgstr "더이상 메시지를 표시하지 않습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Download"
msgstr "다운로드"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "더미"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "From"
msgstr "출발"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr "In #{kanban_getcolorname(record.color.raw_value)}"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "품목 식별을 위한 국제 아티클 번호"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Inventory Adjustment"
msgstr "재고 조정"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Inventory Adjustments"
msgstr "재고 조정"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "재고 공간"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr "LN/SN 상품 명세"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "최근 수정일"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "최근 갱신한 사람"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "최근 갱신 일자"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Leave it"
msgstr "그대로 두기"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "위치"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "처리된 위치"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Lot"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_production_lot
msgid "Lot/Serial"
msgstr "LOT/일련번호"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "로트/일련번호 세부사항"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "이동 명세"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Next"
msgstr "다음"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No %s ready for this product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "내부 작업 유형이 없습니다. 창고 설정에서 하나를 구성하십시오."

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No product found for barcode %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "명명법"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.picking_view_kanban_inherit_barcode
msgid "Open picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
#, python-format
msgid "Open picking form"
msgstr "선별 양식 열기"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "작업"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "소유자"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "포장품"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Package type %s was correctly applied to the package %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "포장품목"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "선별"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "선별 세부사항"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "선별 유형"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.js:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Please, Scan again !"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Previous"
msgstr "이전"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Barcodes PDF"
msgstr "바코드 PDF 인쇄"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Delivery Slip"
msgstr "배송 전표 인쇄"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Print Inventory"
msgstr "재고 인쇄"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Picking Operations"
msgstr "선별 작업 인쇄"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "품목"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "상품 바코드"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "품목 이동 (재고 이동 상세)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "품목 포장법"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "품목 단위"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Put In Pack"
msgstr "꾸러미에 넣기"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr "수량 완료됨"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr "수량 예약됨"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "수량"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr "수량 완료됨"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "예약된 수량"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "수량"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Remove it"
msgstr "제거하기"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan a"
msgstr "스캔"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan a product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Scan a product to filter the transfers."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/scan_product_tip.xml:0
#, python-format
msgid "Scan a product to filter your records"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan an"
msgstr "스캔"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr "더 많은 제품을 스캔하거나 새로운 원래 위치를 스캔"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr "더 많은 제품을 스캔하거나 대상 위치를 스캔"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the serial number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the source location, or scan a product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid ""
"Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible "
"with the product's one (%s)."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_scrap
msgid "Scrap"
msgstr "폐기"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "상품 선택"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "일련번호/로트 번호"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "원래 위치"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "원 꾸러미 상품"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr "재고 바코드 로트"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr "재고 바코드 로트 명세"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Tap to scan"
msgstr "탭하여 스캔"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr "바코드 \"%(barcode)s\"가 올바른 제품, 꾸러미 상품 또는 위치에 해당되지 않습니다."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr "재고조사가 승인되었습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr "스캔한 일련번호가 이미 사용되었습니다."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr "이송이 취소되었습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been validated"
msgstr "이송이 승인되었습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "This package is already scanned."
msgstr "이 꾸러미 상품은 이미 스캔되었습니다."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is already done"
msgstr "이 선별은 이미 완료되었습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "This serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "To"
msgstr "도착"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr "진행 대기"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "To use packages, enable 'Delivery Packages' from the settings"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "전송"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "재고 이동을 통해 한 공간에서 다른 공간으로 품목을 이동할 수 있습니다."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "단위"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "단위"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Validate"
msgstr "승인"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr "승인된 Lot"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "스캔된 마지막 바코드의 값."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr "기능을 탐색할 수 있도록 바코드가 있는 몇 가지 데모 데이터를 만들었습니다. 인쇄"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr "특정 제품에 대한 SN/LN을 스캔하는 마법사"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Wrong Unit of Measure"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "잘못된 바코드"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr "하나 이상의 제품을 스캔해야 합니다."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr "동일한 일련번호를 두 번 스캔할 수 없습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "commands for Inventory"
msgstr "재고 명령"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "document"
msgstr "문서"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "location"
msgstr "위치"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "operation type"
msgstr "작업 유형"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "product"
msgstr "품목"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr "재고 바코드 표"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr "이 모듈이 무엇을 할 수 있는지 확인하십시오! 바코드를 인쇄할 수도 있습니다"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr "이 위치에서 새 이송을 만들기."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer."
msgstr "새로운 이송 만들기."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to open it."
msgstr "그것을 열기."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to show its location and quantity."
msgstr ""
