# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Spanish (Mexico) (https://www.transifex.com/odoo/teams/41243/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: es_MX\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Paquete\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Source Package\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Paquete de origen\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\" "
"attrs=\"{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Número de serie/de "
"lote\" attrs=\"{'invisible': [('tracking', 'not in', ['serial', "
"'lot'])]}\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Número de lote / "
"serie\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Counted Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Cantidad contada\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Quantity\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Cantidad\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cubes mr-3\" title=\"Locations\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-cubes mr-3\" title=\"Ubicaciones\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Ubicación de "
"destino\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Package\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Paquete de "
"destino\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker mr-3\" title=\"Location\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-map-marker mr-3\" title=\"Ubicación\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center mr-3\" title=\"Source "
"Location\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center mr-3\" title=\"Ubicación"
" de origen\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-tags mr-3\" title=\"Product\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-tags mr-3\" title=\"Producto\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-user-o mr-3\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-fw fa-lg fa-user-o mr-3\" title=\"Propietario\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-user-o text-center mr-3\" title=\"Owner\"/>"
msgstr ""
"<i class=\"fa fa-fw fa-lg fa-user-o text-center mr-3\" "
"title=\"Propietario\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr "<i class=\"fa fa-lg fa-archive\" title=\"Paquete\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr "<i class=\"fa fa-lg fa-user-o\" title=\"Propietario\"/>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> Imprimir comandos de códigos de barras"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""
"<i class=\"fa fa-print\"/> Imprimir hoja de demostración de códigos de "
"barras"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "<span> / </span>"
msgstr "<span> / </span>"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<span>On Hand</span>"
msgstr "<span>A la mano</span>"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""
"Un producto rastreado por números de series no puede tener múltiples "
"cantidades para el mismo número de serie."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Add Product"
msgstr "Agregar producto"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Asignación"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""
"Se escaneó un tipo de paquete que no existe. No se puede procesar esta parte"
" del código de barras."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation ?"
msgstr "¿Está seguro de que desea cancelar esta operación?"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#, python-format
msgid "Barcode"
msgstr "Código de barras"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr "Acción del cliente de código de barras de inventario"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Nomenclatura del código de barras"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "Acción del cliente de recolección de código de barras"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "Código de barras escaneado"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "Código de barras escaneado"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""
"El escaneo del código de barras es ambiguo con varios modelos: %s. Utilice "
"el más probable."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Cancel"
msgstr "Cancelar"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr "Cancelar la operación"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr "Cancelar operación"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Cancel this operation ?"
msgstr "¿Cancelar esta operación?"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Close"
msgstr "Cerrar"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Ajustes de configuración"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Configurar códigos de barras de productos"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Confirm"
msgstr "Confirmar"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "Contacto"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "Cantidades contadas"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "Crear un nuevo traslado"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Creado el"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr "Movimiento predeterminado"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr "Datos demo activos"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Description"
msgstr "Descripción"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_dest_id
msgid "Destination Location"
msgstr "Ubicación de destino"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "Paquete de destino"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Discard"
msgstr "Descartar"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Nombre en pantalla"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"¿Desea eliminar este mensaje de forma permanente?\n"
"                No volverá a aparecer, asegúrese de que no necesita la hoja de códigos de barras, o de que tiene una copia."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr "No cancelar"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Don't show this message again"
msgstr "No volver a mostrar este mensaje"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Download"
msgstr "Descargar"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "Prueba"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "From"
msgstr "Desde"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr "En #{kanban_getcolorname(record.color.raw_value)}"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr ""
"Número de artículo internacional utilizado para la identificación de "
"producto."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Ajuste de inventario"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Inventory Adjustments"
msgstr "Ajustes de inventario"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Ubicaciones de inventario"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr "Líneas de producto de números de lote / serie"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Última modificación el"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Leave it"
msgstr "Dejar"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "Ubicación"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "Ubicación procesada"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Lote"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Número de lote / de serie "

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "Detalles del número de lote / serie"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Línea de movimiento"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Next"
msgstr "Siguiente"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No %s ready for this product"
msgstr "No hay %s listos para este producto"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""
"No se encuentra ningún tipo de operación interna. Por favor, configure uno "
"en los ajustes del almacén."

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""
"No hay recolección, ubicación o producto que corresponda con el código de "
"barras %(barcode)s"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""
"No hay recolección o producto que corresponda con el código de barras "
"%(barcode)s"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No product found for barcode %s"
msgstr "No se encontró ningún producto para el código de barras %s"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenclatura"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.picking_view_kanban_inherit_barcode
msgid "Open picking"
msgstr "Abrir recolección"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
#, python-format
msgid "Open picking form"
msgstr "Formulario de recolección abierta"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Operaciones"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "Propietario"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "Paquete"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Package type %s was correctly applied to the package %s"
msgstr "El tipo de paquete %s se aplicó correctamente al paquete %s"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "Paquetes"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Recolección"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "Detalles de recolección"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Tipo de recolección"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.js:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Please, Scan again !"
msgstr "¡Vuélvalo a escanear!"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Previous"
msgstr "Anterior"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Barcodes PDF"
msgstr "Imprimir códigos de barras en PDF"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Delivery Slip"
msgstr "Imprimir recibo de entrega"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Print Inventory"
msgstr "Imprimir inventario"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Picking Operations"
msgstr "Imprimir operaciones de recolección"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "Producto"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Códigos de barras de productos"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Movimientos de producto (línea de movimiento de inventario)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "Embalaje del producto"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr "Cantidad de existencias de producto"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Unidad de medida del producto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Put In Pack"
msgstr "Incluir en el paquete"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr "Cant. hecha:"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr "Cant. reservada:"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Cantidad"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr "Cantidad hecha"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Cantidad reservada"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Remove it"
msgstr "Remover"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan a"
msgstr "Escanear "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan a product"
msgstr "Escanear producto"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Scan a product to filter the transfers."
msgstr "Escanee un producto para filtrar los traslados."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/scan_product_tip.xml:0
#, python-format
msgid "Scan a product to filter your records"
msgstr "Escanee un producto para filtrar sus registros"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan an"
msgstr "Escanear"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr "Escanee más productos, o escanee una nueva ubicación de origen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr "Escanee más productos, o escanee la ubicación de destino"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the lot number of the product"
msgstr "Escanee el número de lote del producto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the serial number of the product"
msgstr "Escanee el número de serie del producto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the source location, or scan a product"
msgstr "Escanee la ubicación de origen, o escanee un producto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid ""
"Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible "
"with the product's one (%s)."
msgstr ""
"La cantidad escaneada utiliza %s como unidad de medida, pero esta UdM no es "
"compatible con la del producto (%s)."

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_scrap
msgid "Scrap"
msgstr "Desechar"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "Seleccionar un producto"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "Número de serie / lote"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr "Mostrar la nomenclatura del código de barras"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "Ubicación de origen"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "Paquete de origen"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr "Lote de código de barras de existencias"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr "Línea de lote de código de barras de existencias"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "Tipo de paquete de existencias"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Tap to scan"
msgstr "Toque para escanear"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""
"El código de barras \"%(barcode)s\" no corresponde al producto, paquete o "
"ubicación adecuados."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr "Se ha validado el ajuste de inventario"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited"
msgstr "La recolección es %s y no puede ser editada"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr "El número de serie escaneado ya está en uso."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr "Se ha cancelado el traslado"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been validated"
msgstr "Se ha validado el traslado"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "This package is already scanned."
msgstr "Ya se ha escaneado este paquete."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is already done"
msgstr "Ya se ha hecho esta recolección"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is cancelled"
msgstr "Está recolección está cancelada"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "This serial number is already used."
msgstr "Este número de serie ya está en uso."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "To"
msgstr "A"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr "Por pocesar"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "To use packages, enable 'Delivery Packages' from the settings"
msgstr "Para usar paquetes, habilite \"paquetes de entrega\" en los ajustes"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "Traslado"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr "Nombre del traslado"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr "Los traslados le permiten mover productos de una ubicación a otra."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Unidad de medida"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "UdM"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Validate"
msgstr "Validar "

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr "Validar lote"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Valor del último código de barras escaneado."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""
"Hemos creado algunos datos demo con códigos de barras para que explore las "
"funciones. Imprima la"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr ""
"Asistente para escanear números de serie / lote para productos específicos"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Wrong Unit of Measure"
msgstr "Unidad de medida equivocada"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "Código de barras incorrecto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""
"Debe escanear uno o más productos o un paquete disponible en la ubicación de"
" la recolección"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr "Debe escanear uno o más productos."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""
"No puede aplicar un tipo de paquete. Primero, escanee un producto o "
"seleccione una línea"

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr "No puede escanear el mismo número de serie dos veces"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "commands for Inventory"
msgstr "comandos para Inventario"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "document"
msgstr "documento"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "location"
msgstr "ubicación"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "operation type"
msgstr "tipo de operación"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "product"
msgstr "producto"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr "hoja de códigos de barras de existencias"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""
"para ver lo que este módulo puede hacer! También puede imprimir el código de"
" barras"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr "para crear un nuevo traslado desde esta ubicación."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer."
msgstr "para crear un nuevo traslado."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to open it."
msgstr "para abrirlo."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to show its location and quantity."
msgstr "para mostrar su ubicación y cantidad."
