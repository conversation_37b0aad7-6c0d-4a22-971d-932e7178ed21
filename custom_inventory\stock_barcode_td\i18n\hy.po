# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * stock_barcode
# 
# Translators:
# <PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 10.0e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-09-07 09:57+0000\n"
"PO-Revision-Date: 2016-09-07 09:57+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2016\n"
"Language-Team: Armenian (https://www.transifex.com/odoo/teams/41243/hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "(make sure your scanner uses carriage return suffix)"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
").\n"
"                            Then, print it via the <i>Print</i> menu and stick it in a visible and convenient place."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>Actions barcodes:</strong> use the barcodes from"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>EAN-13 barcodes:</strong> used by most retail products, they cannot be made\n"
"                            up without proper authorization: you must pay the International\n"
"                            Article Numbering Association a fee in exchange for an EAN code\n"
"                            sequence (that's why no two products in a store will ever have the\n"
"                            same EAN code). Still, as Odoo supports any string as a barcode, so\n"
"                            you can always define your own barcode format for internal use."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>Enjoy your Inventory management!</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>From the Barcode application:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Good location nomenclature:</strong> <i>warehouse short name - location short name - (Corridor X - Shelf Y - Height Z) </i><br/>\n"
"                                Example: A032-025-133 (Note that you can use any string in the barcode field)"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Lots Numbers</strong> can be encoded from incoming shipments, "
"internal moves and outgoing deliveries:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Product variants:</strong> be careful to add barcodes directly on "
"the variant, and not the template product (otherwise you won't be able to "
"differentiate them)."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Scan barcodes on picking lists:</strong> suitable if a significant "
"percentage of your products do not have a barcode on them."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Scan barcodes on products:</strong> suitable if all your products "
"already have a barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>Scan lots or serial numbers:</strong> this is more time consuming, "
"but allows for a full traceability of the parts. It's usually used by "
"manufacturers of sensitive products."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>Test your configuration:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>The next step is to assign barcodes to your products,</strong> by "
"setting the right value in the Barcode field of the Product form."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>To attribute a barcode to a Location,</strong> simply enter one on "
"the Location form (accessible from your"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>To make an Internal Transfer:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>To process Delivery Orders from a computer or mobile "
"device:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>To process printed Delivery Orders:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<strong>We've tested a few devices for you:</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"<strong>What is the difference between Lots and Serial Numbers?</strong>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Automatic carriage return"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Automatic carriage return: <span class=\"label\">OFF</span>"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line_product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_pack_operation_product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap_product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
msgid "Barcode"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:8
#, python-format
msgid "Barcode Scanning"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Before we start, you should choose your working process. There are three "
"suitable approaches to work with barcodes:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"But better yet is to transform this naming into a barcode that can be "
"scanned easily and without error."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"By default, Odoo has a 50 milliseconds delay between each successive scan "
"(it helps avoid accidental double scanning). If you want to suppress this "
"delay, you can configure your scanner to insert a carriage return symbol at "
"the end of each barcode. This is usually the default configuration and can "
"be explicitly configured by scanning a specific barcode in your scanner user"
" manual ('CR suffix ON', 'Apply Enter for suffix', etc.)"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Choose a USB barcode scanner if you plan to scan products at the computer "
"station"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Click on 'Inventory'"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.stock_config_view_form_inherit_stock_barcode
msgid "Configure Product Barcodes"
msgstr ""

#. module: stock_barcode
#: model:web.planner,tooltip_planner:stock_barcode.planner_barcode
msgid "Configure and learn how to use your Barcode Scanner."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Congratulations, you're ready to go live!"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:42
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:45
#, python-format
msgid "Don't show this message again"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"For the Odoo Team,<br/>\n"
"                        Fabien Pinckaers, Founder"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Go for a wireless scanner if you want to scan products at different "
"locations"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Here, you have three options:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"If this product should be manage by lots, a window opens to help you scan "
"the lots / serial numbers"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"If you have a static computer station, it's better to use 'pistols' formats "
"as they are more practical to handle and aim."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"If you use a mobile device, you may prefer a smaller format that is "
"connected in Bluetooth."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "In Odoo, there are two types of internal transfers:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "In the barcode interface, scan the products you want create a lot from"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Inateck BCST-20:"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_inventory_line_product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_pack_operation_product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap_product_barcode
msgid "International Article Number used for product identification."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:20
#: model:ir.model,name:stock_barcode.model_stock_inventory
#, python-format
msgid "Inventory"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory_line
msgid "Inventory Line"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Keyboard layout"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:51
#, python-format
msgid "Leave it"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "List of Locations"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Load all the Delivery Orders marked as \"To Do\", and open the first one."
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_pack_operation_location_processed
msgid "Location processed"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Lot numbers are attributed to several identical products, so each time you "
"scan a Lot Number, Odoo will add one on the product count."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Motorola CS3000:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Motorola DS4208-SR:"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Move to the next Delivery Order to process by clicking on the top-right "
"right arrow or scanning the Pager-Next barcode action."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Naming the locations within your warehouse(s) is crucial for a good "
"inventory management."
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_inventory_action_new_inventory
msgid "New Inventory"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:69
#, python-format
msgid "No internal picking type. Please configure one in warehouse settings."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:54
#, python-format
msgid "No lot found"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:23
#, python-format
msgid "No picking corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:21
#, python-format
msgid "No picking or location corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Note that Bluetooth connections are suitable for maximum 30 feet (10 meters)"
" so you will have to scan products nearby the computer station."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Now that all the flows have been tested and configured, it's time to go into"
" production. Do a few delivery orders as an example with your workers and "
"train them on your favorite process."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Odoo supports most USB, Wireless and Bluetooth barcode scanners (as they all"
" emulate a keyboard)."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"On the opposite, a Serial Number is unique, and represented by only one "
"barcode, sticked on only one item. This means that Odoo won't accept "
"scanning the same Serial Number more than once per operation."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Once it's fully working, give us some feedback: we love to hear from our "
"customer. It would be great if you can send a photo of your warehouse to"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Once you scan the next product or the validate barcode, the window will "
"close automatically"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Once you scanned all products, scan the Validate barcode action to finish "
"the operation."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Once you start processing your delivery orders, scan the barcode on the top-"
"right corner of the document to load the right record on the screen."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:19
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_pack_operation
msgid "Packing Operation"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Pick up and scan each listed product."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Pick up and scan the products"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
msgid "Picking"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/picking_barcode_handler.js:39
#, python-format
msgid "Picking %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_web_planner
msgid "Planner"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Print delivery orders of the day by selecting all documents from the \"To "
"Do\" list and print \"Picking Lists\" from the top menu."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Print this barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:48
#, python-format
msgid "Remove it"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:18
#, python-format
msgid "Scan a document to open it."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:18
#, python-format
msgid "Scan a location to create a new transfer from this location."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Scan a lot barcode, type one manually or leave empty to generate one "
"automatically"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Scan all the products of the location (if you have 5 identical articles, "
"scan it 5 times, or use the keyboard to set the quantity)"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Scan or enter each barcode by manually editing the products or"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Scan the bracode here"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Scan the destination location"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Scan the location's barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Scan the source location, starting from the home of the barcode application"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_scan_location_id
msgid "Scanned Location"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Set barcodes at the initial import of your products or"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:275
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/picking_barcode_handler.js:39
#, python-format
msgid "The picking is %s and cannot be edited."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "The picking type determines the picking view"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Then, scan the barcode of every product, or scan the barcode of the product "
"on the picking line if the barcode on the product is not easily accessible, "
"visible or is missing."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"There are two approaches to process delivery orders: you can either work on "
"printed documents (and scan lines on the documents), or on a screen (and "
"scan products directly)."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:55
#, python-format
msgid ""
"There is no production lot for \"%(product)s\" corresponding to "
"\"%(barcode)s\""
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Those created by a worker (for example, through the internal transfer area "
"of the dashboard)."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Those initiated automatically by the system (for example, a quality control)"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Do"
msgstr "Անելիք"

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Receive"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
msgid "Transfer"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "Transfers"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"USB only, omnidirectional (eliminates the need to align bar code and "
"scanner), rugged design, about $250"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "USB, Wireless or Bluetooth?"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Use our"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Validate the Transfer to finish it"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:10
#, python-format
msgid ""
"We have created a few demo data and barcodes for you to explore the "
"features. Print the"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"When you've picked all the items, click the Validate button or scan the "
"Validate barcode action to finish the Operation."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"When you've scanned all the items of the location, validate the inventory "
"manually or by scanning the 'Validate' barcode ("
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Which scanner format?"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "Wireless and USB, laser, rational choice, about $50"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:274
#, python-format
msgid "Wrong barcode"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"You can set up a computer station or have the worker use a mobile device "
"connected through wireless (phone, tablet or scanner with integrated "
"screen)."
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:23
#: code:addons/stock_barcode/models/inherited_stock_picking.py:38
#, python-format
msgid "You have already scanned the serial number \"%(barcode)s\""
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/inherited_stock_picking.py:22
#: code:addons/stock_barcode/models/inherited_stock_picking.py:37
#, python-format
msgid "You have entered this serial number already"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"You've decided to implement barcodes in your company? Great idea. This tool "
"will help you setup the environment to make it work."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"Your barcode scanner needs to be configured to use the same keyboard layout "
"as your operating system. Otherwise, your scanner won't translate characters"
" correctly (replacing a 'A' with a 'Q' for example). Most scanners are "
"configured by scanning the appropriate barcode in the user's manual."
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "available here"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "compact wireless, about $250"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "fast scanning interface"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "<EMAIL>"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "stock barcodes sheet"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_scrap
msgid "stock.scrap"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "then scan it, the result should be <i>YES IT WORKS.</i>"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid "this document"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:14
#, python-format
msgid "to check out what this module can do !"
msgstr ""

#. module: stock_barcode
#: model:ir.ui.view,arch_db:stock_barcode.barcode_planner
msgid ""
"to launch actions in Odoo like Save, Next Item or Validate instead of using "
"your mouse or keyboard."
msgstr ""
