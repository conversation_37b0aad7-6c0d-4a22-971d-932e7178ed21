.o_barcode_client_action {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: white;

    .o_strong {
        font-weight: bold;
    }

    // Top navbar
    // =====================================
    .o_barcode_header {
        flex: 0 0 46px;
        background-color: $o-brand-odoo;
        color: white;

        .nav-link {
            cursor: pointer;
        }
         .nav-link, .navbar-text {
            font-size: 16px;
        }
    }

    // Top Block
    // =====================================
    .o_barcode_message {
        box-shadow: inset 0 0 20px $gray-900;

        .o_barcode_pic {
            display: flex;
            align-items: center;
            flex: 1 1 60%;
            max-width: 200px;
            .fa-exclamation-triangle {
                opacity: 0.8;
            }
        }
    }

    // Summary
    // =====================================
    .o_barcode_lines_header {
        font-size: 16px;
        background-color: $o-brand-odoo;
        color: white;

        @include media-breakpoint-down(sm) {
            font-size: 14px;
        }

        &:empty {
            display: none;
        }
        .o_barcode_location_label {
            padding: 1px 0 0;
            line-height: 19px;
        }
        .o_barcode_summary_location_src, .o_barcode_summary_location_dest {
            position: relative;
            padding: 1px 4px 0 2px;

            @include media-breakpoint-down(xs) {
                // Used to display the location list on all width on small
                // screen instead of below the current location.
                position: initial;
            }

            .o_current_location, .o_current_dest_location {
                border-bottom: 1px solid white;
            }
            &.o_strong {
                background-color: transparent;
            }
            &.o_clickable {
                cursor: pointer;

                &:hover {
                    background-color: white;
                    color: $o-enterprise-color;
                }
            }

            .o_barcode_list_locations {
                width: max-content;
                position: absolute;
                top: 0;
                left: 0;
                padding: 0;
                text-align: left;
                color: $gray-800;
                background-color: white;
                border: 1px solid $o-brand-secondary;
                z-index: 1;

                @include media-breakpoint-down(xs) {
                    width: 100%;
                }
            }
        }
    }

    // Lines Block
    // =====================================
    .o_barcode_lines {
        clear: both;
        flex: auto;
        overflow: auto;
        box-shadow: inset 0 -7px 4px -5px $gray-300;
        color: $gray-800;
        margin-bottom: 120px;

        &.o_js_has_highlight .o_barcode_line.o_highlight {
            &.o_highlight_green {
                box-shadow: inset 0px 0px 0px 3px $o-brand-secondary;
            }

            .product-label, .o_barcode_scanner_qty {
                color: $headings-color;
            }

            .qty-done, .inventory_quantity {
                font-weight: bold;

                &.o_js_qty_animate {
                    animation: o_barcode_scanner_qty_update .2s alternate;
                }
            }
        }
    }

    // Embedded views
    // =====================================
    .o_barcode_generic_view {
        flex: 1;
        overflow: auto;
        margin-bottom: 30px;

        .o_view_controller, .o_view_controller .o_form_view.o_form_nosheet {
            height: 100%;
            flex-grow: 1;
            padding-top: 0;
        }

        .o_kanban_record {
            font-size: 0.6em;
        }

        .o_form_view.o_xxs_form_view {
            .o_td_label > .o_form_label {
                color: $gray-900;
                font-weight: bold;
                padding-top: 5px;
            }
            .o_field_widget {
                font-size: 1em;
                .btn.fa {
                    font-size: 1em;
                }
            }
            .o_list_view {
                th, .o_field_widget {
                    font-size: $font-size-base;
                }
            }
        }
    }

    // Settings menu
    // =====================================
    .o_barcode_settings {
        display: flex;
        flex: auto;

        > button {
            flex: 1 0 auto;
            border-bottom: 1px solid $gray-700;

            &:last-child {
                border-bottom: 0;
            }
        }
    }

    // Control buttons (validate, previous,
    // next, put in pack, ...)
    // =====================================
    .o_barcode_control {
        flex: 0 0 60px;
        margin: 0 -1px;
        width: 100%;
        > .btn {
            flex: 1;
            width: 50%;
            height: 60px;
            border-width: 1px 0 0 0;
            border-style: solid;
            &.btn-secondary {
                color: $gray-800;
                border-color: $gray-400;
            }
            &.btn-primary {
                border-color: $primary;
            }
            &.btn-success {
                border-color: $success;
            }
            &[disabled] {
                opacity: 1;
                background-color: $gray-200;
                color: $btn-link-disabled-color;
            }
            + .btn {
                border-left-width: 1px;
                border-left-color: $gray-400;
            }
        }
        .fa-angle-left, .fa-angle-right {
            font-size: 1.5em;
        }
        &:first-of-type {
            box-shadow: 0 -3px 10px $gray-300;
        }
    }

    // Line form
    // =====================================
    .o_barcode_line_form {
        margin-left: 24px;
        font-size: 1.4em;

        @include media-breakpoint-down(sm) {
            margin: 0;
        }

        .row {
            width: 700px;

            @include media-breakpoint-down(sm) {
                width: 100vw;
            }

            &.row-long {
                width: 100%;
            }

            a.o_field_widget {
                display: inline-block;
                padding-top: 8px;
            }

            // Avoids to make the UoM field as long as the quantity done field.
            .o_field_widget[name="product_uom_id"] input {
                @include media-breakpoint-up(sm) {
                    min-width: 0;
                }
            }

            & > div {
                .o_input {
                    padding: 8px;
                    border: 1px solid $o-form-lightsecondary;

                    @include media-breakpoint-up(sm) {
                        min-width: 510px;
                        max-width: 510px;
                    }
                }

                .o_required_modifier .o_input {
                    border-bottom: 2px solid $o-main-text-color;
                }

                .o_dropdown_button {
                    display: none;
                }

                i {
                    min-width: 24px;
                    max-width: 24px;
                    color: $o-main-color-muted;
                }
            }
        }
    }
}
