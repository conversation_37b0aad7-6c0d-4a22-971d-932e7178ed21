# -*- coding: utf-8 -*-

{
    'name': "QR code scan for TD",
    'summary': "Use barcode scanners to process operations",
    'description': """
This module enables the barcode scanning feature for the warehouse management system.
    """,
    'category': 'Inventory/Facility',
    'sequence': 259,
    'version': '1.0',
    'depends': ['barcodes_gs1_nomenclature', 'stock_td', 'web_tour', 'web_mobile'],
    'data': [
        'security/ir.model.access.csv',
        'views/stock_inventory_views.xml',
        'views/stock_picking_views.xml',
        'views/stock_move_line_views.xml',
        'views/stock_barcode_views.xml',
        # 'views/res_config_settings_views.xml',
        'views/stock_scrap_views.xml',
        'views/stock_location_views.xml',
        'wizard/stock_barcode_cancel_operation.xml',
        'wizard/stock_barcode_lot_view.xml',
        'data/data.xml',
    ],
    'demo': [
        'data/demo.xml',
    ],
    'installable': True,
    'application': True,
    'license': 'LGPL-3',
    'assets': {
        'web.assets_backend': [
            'stock_barcode_td/static/src/**/*.js',
            'stock_barcode_td/static/src/**/*.scss',
        ],
        'web.assets_qweb': [
            'stock_barcode_td/static/src/**/*.xml',
        ],
        'web.qunit_suite_tests': [
            'stock_barcode_td/static/tests/units/**/*',
        ],
        'web.assets_tests': [
            'stock_barcode_td/static/tests/tours/**/*',
        ],
    }
}
