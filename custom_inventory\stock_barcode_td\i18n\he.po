# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2019
# <AUTHOR> <EMAIL>, 2019
# <PERSON><PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# <AUTHOR> <EMAIL>, 2019
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~12.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-08-26 08:05+0000\n"
"PO-Revision-Date: 2019-08-26 09:38+0000\n"
"Last-Translator: ZVI BLONDER <<EMAIL>>, 2019\n"
"Language-Team: Hebrew (https://www.transifex.com/odoo/teams/41243/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % 1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> הדפס פקודות ברקוד"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Add product"
msgstr "הוסף מוצר "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_form_view_inherit
#, python-format
msgid "Barcode"
msgstr "ברקוד"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Adjustment Client Action"
msgstr "מלאי ברקוד התאמת פעולת הלקוח"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "נומנקלטורת ברקוד"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr "ברקוד בחירת לקוח פועל"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Barcode Scanned"
msgstr "ברקוד סרוק"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "סריקת ברקוד"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
#, python-format
msgid "Cancel"
msgstr "בטל"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Close"
msgstr "סגור"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "הגדרות תצורה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "הגדר ברקודים של מוצרים"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Confirm"
msgstr "אשר"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "נוצר על-ידי"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "נוצר ב-"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr "תנועת ברירת מחדל"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid ""
"Delivery Packages needs to be enabled in Inventory Settings to use packages"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Discard"
msgstr "בטל"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "הצג שם"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"האם ברצונך להסיר את ההודעה הזו לצמיתות?\n"
"זה לא יופיע יותר, אז ודא שאתה לא צריך את גיליון ברקודים או שיש לך עותק."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Don't show this message again"
msgstr "אל תציג הודעה זו שוב"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Download"
msgstr "הורדה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_inventory_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
msgid "Dummy"
msgstr "דמה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Edit"
msgstr "ערוך"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "From"
msgstr "מאת"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "From:"
msgstr "מאת:"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "תעודה מזהה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "מספר זיהוי בינלאומי המשמש להגדרת המוצר."

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory
msgid "Inventory"
msgstr "מלאי"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
#, python-format
msgid "Inventory Adjustments"
msgstr "התאמות מלאי"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "Inventory Details"
msgstr "פרטי מלאי"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_inventory_line
msgid "Inventory Line"
msgstr "קוי מלאי"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "מקומות המלאי"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr "שורות מספר אצווה/ מספר סידורי של מוצר"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "שינוי אחרון ב"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "עודכן לאחרונה על-ידי"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "עדכון אחרון ב"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Leave it"
msgstr "עזוב את זה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "מיקום העיבוד"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "אצווה"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "פרטי אצווה/מספר סידרתי"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "להזיז קו"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_inventory_action_new_inventory
msgid "New Inventory"
msgstr "מלאי חדש"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Next"
msgstr "הבא"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr "אין סוג פעולה פנימי. נא להגדיר אחת בהגדרות המחסן."

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid "No picking corresponding to barcode %(barcode)s"
msgstr " אין איסוף המתאימה לברקוד%(barcode)s"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#, python-format
msgid "No picking or location corresponding to barcode %(barcode)s"
msgstr "אין איסוף או מיקום המתאים לברקוד %(barcode)s"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.open_picking
msgid "Open a picking"
msgstr "פתח ליקוט"

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/main.py:0
#: code:addons/stock_barcode/models/stock_picking.py:0
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Open picking form"
msgstr "פתח טופס ליקוט"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "פעולות"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "ליקוט"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:0
#, python-format
msgid "Picking %s"
msgstr "ליקוט %s"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr "איסוף פריטים"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "סוג איסוף"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Previous"
msgstr "קודם"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Barcodes PDF"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Barcodes ZPL"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Delivery Slip"
msgstr "הדפיס שובר משלוח"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Inventory"
msgstr "הדפס רשימת מלאי"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Print Picking Operations"
msgstr "הדפס פעולות ליקוט"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
msgid "Product"
msgstr "מוצר"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "ברקודי מוצר "

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "תנועות המוצר (תנועת קו האחסון)"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Put In Pack"
msgstr "הכנס לחבילה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr "כמות שהושלמה"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr "כמות שמורה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "כמות"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr "כמות גמורה "

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "כמות שמורה "

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_line_barcode
msgid "Real Quantity"
msgstr "כמות אמיתית"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/stock_barcode.js:0
#, python-format
msgid "Remove it"
msgstr "הסר זאת"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Scan a"
msgstr "סרוק "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "Scan an"
msgstr "סרוק "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr "סרוק מוצרים נוספים או סרוק מיקום מקור חדש"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr "סרוק מוצרים נוספים או סרוק את מיקום היעד"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan products"
msgstr "סרוק מוצרים"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan the serial or lot number of the product"
msgstr "סרוק את המספר הסידורי או אצווה של המוצר"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Scan the source location"
msgstr "סרוק את מיקום המקור"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "Scanning is disabled in this state."
msgstr "סריקה אינה זמינה במצב זה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: model:ir.model,name:stock_barcode.model_stock_scrap
#, python-format
msgid "Scrap"
msgstr "השמדות"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "בחר מוצר "

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr "אצוות מלאי ברקוד"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr "שורת אצוות מלאי ברקוד"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "Success"
msgstr "הצלחה"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr "הברקוד \"%(barcode)s\" אינו מתאים למוצר, חבילה או מיקום ."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#, python-format
msgid "The inventory adjustment has been cancelled"
msgstr "התאמת המלאי בוטלה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/inventory_client_action.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr "התאמת המלאי אומתה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited."
msgstr "האיסוף הוא %s ולא ניתן לערוך אותו."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "The scanned lot does not match an existing one."
msgstr "סריקה אינו תואם למיקום קיים."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr "המספר הסידורי הסרוק כבר נמצא בשימוש."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr "ההעברה בוטלה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/picking_client_action.js:0
#, python-format
msgid "The transfer has been validated"
msgstr "ההעברה אומתה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This inventory adjustment is already done"
msgstr "התאמת מלאי זו כבר בוצעה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "This location is not a child of the main location."
msgstr "מיקום זה אינו צאצא של המיקום המרכזי"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "This package is already scanned."
msgstr "חבילה זו כבר סרוקה."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This picking is already cancelled"
msgstr "הבחירה הזו כבר בוטלה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "This picking is already done"
msgstr "הבחירה הזו כבר בוצע"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_inventory_barcode2
msgid "To"
msgstr "אל"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr "לערוך"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "To:"
msgstr "אל:"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
msgid "Transfer"
msgstr "העברה"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "יחידת מידה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#: code:addons/stock_barcode/static/src/xml/qweb_templates.xml:0
#, python-format
msgid "Validate"
msgstr "לאמת"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr "אמת אצווה"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "הערך של הברקוד האחרון שנסרק."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "Warning"
msgstr "אזהרה"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr "יצרנו כמה נתוני הדגמה עם ברקודים לך לחקור את התכונות. הדפס את"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr "אשף לסריקת מספר סידורי / מספר אצווה עבור מוצר מסוים"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "ברקוד שגוי"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan a source location."
msgstr "אתה צפוי לסרוק את מיקום המקור."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan more products or a destination location."
msgstr "אתה צפוי לסרוק מוצרים נוספים או מיקום יעד."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking's location"
msgstr "אתם צפויים לסרוק מוצר אחד או יותר או חבילה לרשותכם מיקום האיסוף"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#: code:addons/stock_barcode/static/src/js/client_action/abstract_client_action.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr "אתה צפוי לסרוק מוצר אחד או יותר."

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr "לא ניתן לסרוק פעמיים את אותו מספר סידורי"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_inventory.py:0
#, python-format
msgid "You must define a warehouse for the company: %s."
msgstr "עליך להגדיר מחסן עבור החברה: %s."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "commands for Inventory"
msgstr "פקודות מלאי "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "document"
msgstr "מסמך"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "location"
msgstr "מיקום"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "operation type"
msgstr "סוג פעולה "

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr "גיליון ברקודים של מלאי"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr "כדי לבדוק מה מודול זה יכול לעשות! ניתן גם להדפיס את הברקוד"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr "כדי ליצור העברה חדשה ממיקום זה."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to create a new transfer."
msgstr "ליצור העברה חדשה."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/xml/stock_barcode.xml:0
#, python-format
msgid "to open it."
msgstr "לפתוח את זה.."
