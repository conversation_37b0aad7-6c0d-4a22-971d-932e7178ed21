from odoo import http
from odoo.http import request

import logging
_logger = logging.getLogger(__name__)

class MgmtProjectsController(http.Controller):

    @http.route(['/qrcode_td','/barcode_td'], type='http', auth="user", website=True)
    def qrcode_scanner_menu(self):
        # Redirect to the backend Management System Audit page QR code scan for TD
        domain = [('name', '=', 'QR code scan for TD')]
        actions_records = http.request.env['ir.ui.menu'].sudo().search(domain, limit=1)

        menu_id = actions_records.id
        action_id = actions_records.action.id if actions_records.action else 'No action'
        url = f'/web#cids=1&menu_id={menu_id}&action={action_id}'
        # /web#cids=1&menu_id=211&action=716

        return request.redirect(url)