# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* stock_barcode
# 
# Translators:
# <PERSON>, 2021
# <PERSON> <<EMAIL>>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server saas~14.5+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-09-14 10:59+0000\n"
"PO-Revision-Date: 2021-09-14 12:43+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2021\n"
"Language-Team: German (https://www.transifex.com/odoo/teams/41243/de/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: de\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-archive mr-3\" title=\"Source Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\" "
"attrs=\"{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-barcode mr-3\" title=\"Serial/Lot Number\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Counted Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cube mr-3\" title=\"Quantity\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-cubes mr-3\" title=\"Locations\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-long-arrow-right mr-3\" title=\"Destination "
"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-map-marker mr-3\" title=\"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid ""
"<i class=\"fa fa-fw fa-lg fa-map-marker text-center mr-3\" title=\"Source "
"Location\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-tags mr-3\" title=\"Product\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<i class=\"fa fa-fw fa-lg fa-user-o mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "<i class=\"fa fa-fw fa-lg fa-user-o text-center mr-3\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-archive\" title=\"Package\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban_2
msgid "<i class=\"fa fa-lg fa-user-o\" title=\"Owner\"/>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode commands"
msgstr "<i class=\"fa fa-print\"/> Drucken Sie die Barcode-Befehle."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "<i class=\"fa fa-print\"/> Print barcode demo sheet"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "<span> / </span>"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "<span>On Hand</span>"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"A product tracked by serial numbers can't have multiple quantities for the "
"same serial number."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Add Product"
msgstr "Produkt hinzufügen"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Allocation"
msgstr "Urlaubsanspruch"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"An unexisting package type was scanned. This part of the barcode can't be "
"processed."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Are you sure you want to cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.client,name:stock_barcode.stock_barcode_action_main_menu
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap__product_barcode
#: model:ir.ui.menu,name:stock_barcode.stock_barcode_menu
#, python-format
msgid "Barcode"
msgstr "Strichcode"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_inventory_client_action
msgid "Barcode Inventory Client Action"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Barcode Nomenclature"
msgstr "Barcode Nomenklatur"

#. module: stock_barcode
#: model:ir.actions.client,name:stock_barcode.stock_barcode_picking_client_action
msgid "Barcode Picking Client Action"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,field_description:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Barcode Scanned"
msgstr "Gescannter Barcode"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Barcode Scanning"
msgstr "Barcode scannen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Barcode scan is ambiguous with several model: %s. Use the most likely."
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Cancel"
msgstr "Abbrechen"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_cancel_operation
msgid "Cancel Operation"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Cancel operation"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Cancel this operation ?"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/components/main.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Close"
msgstr "Schließen"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_config_settings
msgid "Config Settings"
msgstr "Konfiguration "

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.res_config_settings_view_form
msgid "Configure Product Barcodes"
msgstr "Konfigurieren der Produkt-Barcodes"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Confirm"
msgstr "Bestätigen"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Counted Quantity"
msgstr "Gezählte Menge"

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Create a new transfer"
msgstr "Neue Lagerbewegung erstellen"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_uid
msgid "Created by"
msgstr "Erstellt von"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__create_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__create_date
msgid "Created on"
msgstr "Erstellt am"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__default_move_id
msgid "Default Move"
msgstr "Standard-Buchung"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__stock_barcode_demo_active
msgid "Demo Data Active"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Description"
msgstr "Beschreibung"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_dest_id
msgid "Destination Location"
msgstr "Lagerort (Ziel)"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Destination Package"
msgstr "Zielverpackung"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/widgets/views_widget.xml:0
#, python-format
msgid "Discard"
msgstr "Verwerfen"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__display_name
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__display_name
msgid "Display Name"
msgstr "Anzeigename"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid ""
"Do you want to permanently remove this message ?\n"
"                It won't appear anymore, so make sure you don't need the barcodes sheet or you have a copy."
msgstr ""
"Möchten Sie diese Nachricht dauerhaft entfernen?\n"
"Es wird nicht mehr erscheinen, also stellen Sie sicher, dass Sie nicht brauchen die Barcodes Blatt oder Sie haben eine Kopie."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_barcode_cancel_operation_view
msgid "Don't cancel"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Don't show this message again"
msgstr "Diese Meldung nicht mehr anzeigen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Download"
msgstr "Herunterladen"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__dummy_id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_quant__dummy_id
msgid "Dummy"
msgstr "Dummy"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "From"
msgstr "Von"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__id
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__id
msgid "ID"
msgstr "ID"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "In #{kanban_getcolorname(record.color.raw_value)}"
msgstr "In #{kanban_getcolorname(record.color.raw_value)}"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line__product_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap__product_barcode
msgid "International Article Number used for product identification."
msgstr "Internationale Artikelnummer zur Produktidentifikation"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Inventory Adjustment"
msgstr "Inventur"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Inventory Adjustments"
msgstr "Inventur"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_location
msgid "Inventory Locations"
msgstr "Inventurlager"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot_line
msgid "LN/SN Product Lines"
msgstr "LN / SN-Produktlinien"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot____last_update
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line____last_update
msgid "Last Modified on"
msgstr "Zuletzt geändert am"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_uid
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_uid
msgid "Last Updated by"
msgstr "Zuletzt aktualisiert durch"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__write_date
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__write_date
msgid "Last Updated on"
msgstr "Zuletzt aktualisiert am"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Leave it"
msgstr "Lass es"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Location"
msgstr "Ort"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__location_processed
msgid "Location Processed"
msgstr "Ort verarbeitet"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__lot_name
msgid "Lot"
msgstr "Fertigungslos"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_production_lot
msgid "Lot/Serial"
msgstr "Los/Serie"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Lot/Serial Number Details"
msgstr "Los-/Seriennummerdetails"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__move_line_id
msgid "Move Line"
msgstr "Buchungszeile"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Next"
msgstr "Weiter"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No %s ready for this product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid ""
"No internal operation type. Please configure one in warehouse settings."
msgstr ""
"Kein interner Vorgangstyp. Konfigurieren Sie einen in den Lager-"
"Einstellungen."

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or location or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/controllers/stock_barcode.py:0
#, python-format
msgid "No picking or product corresponding to barcode %(barcode)s"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "No product found for barcode %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__barcode_nomenclature_id
msgid "Nomenclature"
msgstr "Nomenklatur"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.picking_view_kanban_inherit_barcode
msgid "Open picking"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#: model:ir.actions.act_window,name:stock_barcode.open_picking
#, python-format
msgid "Open picking form"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_kanban
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_type_action_kanban
#, python-format
msgid "Operations"
msgstr "Vorgänge"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Owner"
msgstr "Besitzer"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Package"
msgstr "Verpackung"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Package type %s was correctly applied to the package %s"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant_package
msgid "Packages"
msgstr "Verpackungen"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.stock_picking_action_form
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__picking_id
msgid "Picking"
msgstr "Kommissionierung"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_barcode
msgid "Picking Details"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking_type
msgid "Picking Type"
msgstr "Vorgangstyp"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.js:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Please, Scan again !"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Previous"
msgstr "Vorherig"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Barcodes PDF"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Delivery Slip"
msgstr "Lieferschein drucken"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Print Inventory"
msgstr "Bestand drucken"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "Print Picking Operations"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_product
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__product_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Product"
msgstr "Produkt"

#. module: stock_barcode
#: model:ir.actions.act_window,name:stock_barcode.product_action_barcodes
#: model_terms:ir.ui.view,arch_db:stock_barcode.product_view_list_barcodes
msgid "Product Barcodes"
msgstr "Produktbarcode"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_move_line
msgid "Product Moves (Stock Move Line)"
msgstr "Produktbewegungen (Lagerbewegung)"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_product_packaging
msgid "Product Packaging"
msgstr "Produktverpackung"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__product_stock_quant_ids
msgid "Product Stock Quant"
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_uom_uom
msgid "Product Unit of Measure"
msgstr "Produkteinheit"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Put In Pack"
msgstr "einpacken"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_done
msgid "Qty Done"
msgstr "Bearbeitete Menge"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__qty_reserved
msgid "Qty Reserved"
msgstr "Reservierte Menge"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Quantity"
msgstr "Menge"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_done
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Quantity Done"
msgstr "Erledigte Menge"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__qty_reserved
msgid "Quantity Reserved"
msgstr "Reservierter Bestand"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_quant
msgid "Quants"
msgstr "Quants"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.js:0
#, python-format
msgid "Remove it"
msgstr "Entfernen Sie es"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan a"
msgstr "Scannen Sie ein"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan a product"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Scan a product to filter the transfers."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/scan_product_tip.xml:0
#, python-format
msgid "Scan a product to filter your records"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Scan an"
msgstr "Scannen Sie ein"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan a new source location"
msgstr ""
"Scannen Sie weitere Produkte oder scannen Sie einen neuen Quelllagerort"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan more products, or scan the destination location"
msgstr "Scannen Sie weitere Produkte oder scannen Sie den Zielort"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the lot number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the serial number of the product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "Scan the source location, or scan a product"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid ""
"Scanned quantity uses %s as Unit of Measure, but this UoM is not compatible "
"with the product's one (%s)."
msgstr ""

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_scrap
msgid "Scrap"
msgstr "Ausschuss melden"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Select a Product"
msgstr "Ein Produkt auswählen"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
msgid "Serial/Lot Number"
msgstr "Serien- / Losnummer"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_res_config_settings__show_barcode_nomenclature
msgid "Show Barcode Nomenclature"
msgstr ""

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_move_line__picking_location_id
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Location"
msgstr "Lagerort (Quelle)"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
msgid "Source Package"
msgstr "Verpackungsquelle"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot_line__stock_barcode_lot_id
msgid "Stock Barcode Lot"
msgstr "Lager-Barcode Los"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_lot__stock_barcode_lot_line_ids
msgid "Stock Barcode Lot Line"
msgstr "Lager-Barcode Loszeile"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_package_type
msgid "Stock package type"
msgstr "Typ des Lagerpakets"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "Tap to scan"
msgstr "Tippen um zu scannen"

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid ""
"The barcode \"%(barcode)s\" doesn't correspond to a proper product, package "
"or location."
msgstr ""
"Der Barcode \"%(barcode)s\" entspricht keinem passenden Produkt, Paket oder "
"Standort."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "The inventory adjustment has been validated"
msgstr "The inventory adjustment has been validated"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/legacy/forms/picking_barcode_handler.js:0
#, python-format
msgid "The picking is %s and cannot be edited"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "The scanned serial number is already used."
msgstr "Die gescannte Seriennummer wird bereits verwendet."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been cancelled"
msgstr "Der transfer wurde abgebrochen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "The transfer has been validated"
msgstr "Die Übertragung wurde bestätigt"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "This package is already scanned."
msgstr "Dieses Paket wurde bereits gescannt."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is already done"
msgstr "Diese Kommissionierung ist bereits abgeschlossen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "This picking is cancelled"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "This serial number is already used."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "To"
msgstr "Bis"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_picking_type_kanban
msgid "To Process"
msgstr "Zu verarbeiten"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "To use packages, enable 'Delivery Packages' from the settings"
msgstr ""
"Um Verpackungen zu nutzen, aktivieren Sie \"Versandverpackungen\" in den "
"Einstellungen"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_picking
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_id
msgid "Transfer"
msgstr "Lieferung vornehmen"

#. module: stock_barcode
#: model:ir.model.fields,field_description:stock_barcode.field_stock_barcode_cancel_operation__picking_name
msgid "Transfer Name"
msgstr ""

#. module: stock_barcode
#: model_terms:ir.actions.act_window,help:stock_barcode.stock_picking_action_kanban
msgid "Transfers allow you to move products from one location to another."
msgstr ""
"Lagerbewegungen erlauben es Ihnen, Produkte von einem zum anderen Lagerort "
"zu bewegen."

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_move_line_product_selector
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.stock_quant_barcode_kanban
msgid "Unit of Measure"
msgstr "Mengeneinheit"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_stock_quant_tree
msgid "UoM"
msgstr "UoM"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/components/main.xml:0
#, python-format
msgid "Validate"
msgstr "Bestätigen"

#. module: stock_barcode
#: model_terms:ir.ui.view,arch_db:stock_barcode.view_barcode_lot_form
msgid "Validate Lot"
msgstr "Los validieren"

#. module: stock_barcode
#: model:ir.model.fields,help:stock_barcode.field_stock_barcode_lot___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_move_line___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_picking___barcode_scanned
#: model:ir.model.fields,help:stock_barcode.field_stock_scrap___barcode_scanned
msgid "Value of the last barcode scanned."
msgstr "Wert des zuletzt gelesenen Barcodes."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid ""
"We have created a few demo data with barcodes for you to explore the "
"features. Print the"
msgstr ""
"Wir haben ein paar Demo-Daten mit Barcodes für Sie erstellt, damit Sie die "
"Funktionen erkunden können. Drucken Sie"

#. module: stock_barcode
#: model:ir.model,name:stock_barcode.model_stock_barcode_lot
msgid "Wizard to scan SN/LN for specific product"
msgstr "Assistent zum Scannen von SN/LN für bestimmte Produkte"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#: code:addons/stock_barcode/static/src/models/barcode_quant_model.js:0
#, python-format
msgid "Wrong Unit of Measure"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/models/stock_picking.py:0
#, python-format
msgid "Wrong barcode"
msgstr "Falscher Barcode"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid ""
"You are expected to scan one or more products or a package available at the "
"picking location"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_model.js:0
#, python-format
msgid "You are expected to scan one or more products."
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/models/barcode_picking_model.js:0
#, python-format
msgid "You can't apply a package type. First, scan product or select a line"
msgstr ""

#. module: stock_barcode
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#: code:addons/stock_barcode/wizard/stock_barcode_lot.py:0
#, python-format
msgid "You cannot scan two times the same serial number"
msgstr "Sie können nicht zweimal dieselbe Seriennummer scannen."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "commands for Inventory"
msgstr ""

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "document"
msgstr "Dokument"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "location"
msgstr "Ort"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "operation type"
msgstr "Vorgangstyp"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "product"
msgstr "Produkt"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "stock barcodes sheet"
msgstr "Lager Barcode Blatt"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to check out what this module can do! You can also print the barcode"
msgstr ""
"um herauszufinden, was dieses Modul kann! Sie können den Barcode auch "
"ausdrucken"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer from this location."
msgstr "um eine neue Übertragung von diesem Speicherort zu erstellen."

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to create a new transfer."
msgstr "Einen neune Transfer erstellen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to open it."
msgstr "es öffnen"

#. module: stock_barcode
#. openerp-web
#: code:addons/stock_barcode/static/src/stock_barcode_menu.xml:0
#, python-format
msgid "to show its location and quantity."
msgstr ""
