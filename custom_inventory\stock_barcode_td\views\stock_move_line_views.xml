<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="stock_move_line_product_selector" model="ir.ui.view">
        <field name="name">stock.product.selector.td</field>
        <field name="model">stock.move.line.td</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <form string="Select a Product" class="o_barcode_line_form">
                <div class="row mt-5 mb-4">
                    <field name="company_id" force_save="1" invisible="1"/>
                    <field name="product_uom_qty" invisible="1" />
                    <field name="move_id" invisible="1" />
                    <field name="state" invisible="1" />
                    <field name="tracking" invisible="1" />
                    <field name="product_uom_category_id" invisible="1" />
                    <field name="picking_code" invisible="1"/>
                    <div class="col-12 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-tags mr-3" title="Product"/>
                        <field name="product_id" placeholder="Product"
                            context="{'default_detailed_type': 'product'}"
                            attrs="{'readonly': ['|', ('state', '=', 'done'), ('move_id', '!=', False)]}"
                            domain="[('type', 'in', ['product', 'consu']), '|', ('company_id', '=', False), ('company_id', '=', company_id)]"
                            required="1" nolabel="1"/>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-cube mr-3" title="Quantity"/>
                        <field name="qty_done" class="w-50 w-md-100" options="{'type': 'number'}" placeholder="Quantity"/>
                        <widget name="set_reserved_qty_button_td"
                            attrs="{'invisible': [('product_uom_qty', '=', 0)]}"/>
                    </div>
                    <div class="col-6 my-2 d-flex align-items-baseline" groups="uom.group_uom"
                        attrs="{'invisible': [('product_uom_qty', '!=', 0)]}">
                        <i class="fa mr-3" attrs="{'invisible': [('product_uom_qty', '!=', 0.0)]}"/>
                        <field name="product_uom_id" placeholder="Unit of Measure"
                            options="{'no_open': True}"
                            attrs="{'readonly': [('product_uom_qty', '!=', 0.0)]}"/>
                    </div>
                </div>
                <!--div class="row justify-content-center justify-content-md-start">
                    <div class="col-10 col-md-8 my-2 ml-md-5 align-items-baseline"
                        attrs="{'invisible': [('tracking', '=', 'serial')]}">
                        <widget name="digipad_td" quantity_field="qty_done"/>
                    </div>
                </div-->
                <div class="row" groups="stock.group_stock_multi_locations">
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline"
                        attrs="{'invisible': [('picking_code', '=', 'incoming')]}">
                        <i class="fa fa-fw fa-lg fa-map-marker text-center mr-3" title="Source Location"/>
                        <field name="location_id" placeholder="Source Location"
                            domain="[('id', 'child_of', picking_location_id)]"
                            options="{'no_create': True}"/>
                    </div>
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline" attrs="{'invisible': [('picking_code', '=', 'outgoing')]}">
                        <i class="fa fa-fw fa-lg fa-long-arrow-right mr-3" title="Destination Location"/>
                        <field name="location_dest_id" placeholder="Source Location"
                            domain="[('id', 'child_of', picking_location_dest_id)]"
                            options="{'no_create': True}"/>
                    </div>
                </div>
                <div class="row" groups="stock.group_tracking_lot">
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-archive mr-3" title="Source Package"/>
                        <field name="package_id" placeholder="Source Package"/>
                    </div>
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-long-arrow-right mr-3" title="Destination Package"/>
                        <field name="result_package_id" placeholder="Destination Package"/>
                    </div>
                </div>

                <div class="row" groups="stock.group_tracking_owner">
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-user-o text-center mr-3" title="Owner"/>
                        <field name="owner_id" placeholder="Owner" required="1"/>
                    </div>
                </div>

                <field name="tracking" invisible="1"/>
                <field name="picking_id" invisible="1"/>
                <field name="picking_type_use_create_lots" invisible="1"/>
                <field name="picking_type_use_existing_lots" invisible="1"/>
                <field name="picking_location_id" invisible="1"/>
                <field name="picking_location_dest_id" invisible="1"/>
                <div class="row" groups="stock.group_production_lot">
                    <div class="col-12 col-md-6 my-2 d-flex align-items-baseline">
                        <i class="fa fa-fw fa-lg fa-barcode mr-3" title="Serial/Lot Number" attrs="{'invisible': [('tracking', 'not in', ['serial', 'lot'])]}"/>
                        <field name="lot_name" placeholder="Serial/Lot Number"
                            attrs="{'invisible': ['|', '|', ('tracking', '=', 'none'), ('picking_type_use_create_lots', '=', False), ('picking_type_use_existing_lots', '=', True)]}"
                            />
                        <field name="lot_id" placeholder="Serial/Lot Number" options="{'no_open': True}"
                            context="{'default_product_id': product_id, 'default_company_id': company_id}"
                            attrs="{'invisible': ['|', ('tracking', '=', 'none'), ('picking_type_use_existing_lots', '=', False)]}"
                            />
                    </div>
                </div>

                <div class="row row-long">
                    <div class="col-12 my-2 d-flex">
                        <i class="fa fa-fw fa-lg fa-cubes mr-3" title="Locations"/>
                        <field name="product_stock_quant_ids" context="{'kanban_view_ref': 'stock_barcode_td.stock_quant_barcode_kanban_2', 'tree_view_ref': 'stock_barcode.view_stock_quant_tree'}"/>
                    </div>
                </div>
            </form>
        </field>
    </record>

    <record id="stock_quant_barcode_kanban" model="ir.ui.view">
        <field name="name">stock.barcode.quant.td.kanban</field>
        <field name="model">stock.quant.td</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <kanban draggable="0">
                <field name="product_id"/>
                <field name="quantity"/>
                <templates>
                    <t t-name="kanban-box">
                        <div>
                            <strong><field name="product_id"/></strong>
                            <div class="row">
                                <div class="col-6">
                                    <field name="lot_id" groups="stock_td.group_production_lot"/>
                                    <field name="quantity" string="Quantity Done"/>
                                    <field name="product_uom_id" string="Unit of Measure" groups="uom.group_uom"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="stock_quant_barcode_kanban_2" model="ir.ui.view">
        <field name="name">stock.quant.kanban.barcode.td</field>
        <field name="model">stock.quant.td</field>
        <field name="priority">1000</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="mb4">
                                <strong><field name="location_id"/></strong>
                            </div>
                            <div class="mb4">
                                <field name="quantity"/> <field name="product_uom_id" groups="uom.group_uom"/>
                                <span class="ml-4" attrs="{'invisible': [('lot_id', '=', False)]}" groups="stock_td.group_production_lot">
                                    <field name="lot_id"/>
                                </span>
                            </div>
                            <div class="mb4" attrs="{'invisible': [('package_id', '=', False)]}" groups="stock_td.group_tracking_lot">
                                <i class="fa fa-lg fa-archive" title="Package"/><field name="package_id"/>
                            </div>
                            <div class="mb4" attrs="{'invisible': [('owner_id', '=', False)]}" groups="stock_td.group_tracking_owner">
                                <i class="fa fa-lg fa-user-o" title="Owner"/><field name="owner_id"/>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="view_stock_quant_tree" model="ir.ui.view">
        <field name="name">stock_barcode.quant.tree.td.inherit</field>
        <field name="model">stock.quant.td</field>
        <field name="inherit_id" ref="stock_td.view_stock_quant_tree"/>
        <field name="mode">primary</field>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='product_uom_id']" position="attributes" >
                <attribute name="string">UoM</attribute>
                <attribute name="optional">show</attribute>
            </xpath>
            <xpath expr="//field[@name='available_quantity']" position="attributes" >
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='company_id']" position="attributes" >
                <attribute name="optional">hide</attribute>
            </xpath>
        </field>
    </record>
</odoo>
